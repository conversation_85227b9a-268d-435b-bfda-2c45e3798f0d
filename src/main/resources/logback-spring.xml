<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <contextName>Logback For demo Mobile</contextName>

    <!-- 设置log日志存放地址 -->
    <!--（改） 单环境设置 -->
    <property name="LOG_HOME" value="app/logs" />
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--  encoder 默认配置为PatternLayoutEncoder  -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L - %msg%n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--     此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息  -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>

    <appender name="ERROR-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--  正在记录的日志文件的路径及文件名  -->
        <file>${LOG_HOME}/log_error.log</file>
        <!--  日志记录器的滚动策略，按日期，按大小记录  -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--  归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
                        而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引  -->
            <fileNamePattern>${LOG_HOME}/log-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--  除按日志记录之外，还配置了日志文件不能超过2M，若超过2M，日志文件会以索引0开始，
                        命名日志文件，例如log-error-2013-12-21.0.log  -->
            <!-- 日志文件保留天数 -->
            <MaxHistory>7</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--  追加方式记录日志  -->
        <append>true</append>
        <!--  日志文件的格式  -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L - %msg%n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--  此日志文件只记录info级别的  -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 按照每天生成日志文件 -->
    <appender name="INFO-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--  正在记录的日志文件的路径及文件名  -->
        <file>${LOG_HOME}/log_info.log</file>
        <!--  日志记录器的滚动策略，按日期，按大小记录  -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--  归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
                        而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引  -->
            <fileNamePattern>${LOG_HOME}/info/log-info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 日志文件保留天数 -->
            <MaxHistory>7</MaxHistory>
            <!--  除按日志记录之外，还配置了日志文件不能超过2M，若超过2M，日志文件会以索引0开始，
                        命名日志文件，例如log-error-2013-12-21.0.log  -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--  追加方式记录日志  -->
        <append>true</append>
        <!--  日志文件的格式  -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L - %msg%n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--  此日志文件只记录info级别的  -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--  异步输出  -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!--  不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志  -->
        <discardingThreshold>0</discardingThreshold>
        <!--  更改默认的队列的深度,该值会影响性能.默认值为256  -->
        <queueSize>512</queueSize>
        <!--  添加附加的appender,最多只能添加一个  -->
        <appender-ref ref="INFO-LOG"/>
    </appender>

    <!-- 全局，控制台遇到INFO及以上级别就进行输出 -->
    <root level="info">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="INFO-LOG"/>
        <appender-ref ref="ERROR-LOG"/>
    </root>

</configuration>