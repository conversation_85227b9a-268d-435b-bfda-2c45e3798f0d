package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "CONFIG_TEMPLATEDATA")
public class ConfigTempLateDataEntity {
    private String tempname;
    private String clobs;
    private Date savedate;
    private String descs;

    @Id
    @Column(name = "TEMPNAME")
    public String getTempname() {
        return tempname;
    }

    public void setTempname(String tempname) {
        this.tempname = tempname;
    }

    @Basic
    @Column(name = "CLOBS")
    public String getClobs() {
        return clobs;
    }

    public void setClobs(String clobs) {
        this.clobs = clobs;
    }

    @Basic
    @Column(name = "SAVEDATE")
    public Date getSavedate() {
        return savedate;
    }

    public void setSavedate(Date savedate) {
        this.savedate = savedate;
    }

    @Basic
    @Column(name = "DESCS")
    public String getDescs() {
        return descs;
    }

    public void setDescs(String descs) {
        this.descs = descs;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfigTempLateDataEntity that = (ConfigTempLateDataEntity) o;
        return Objects.equals(tempname, that.tempname) &&
                Objects.equals(clobs, that.clobs) &&
                Objects.equals(savedate, that.savedate) &&
                Objects.equals(descs, that.descs);
    }

    @Override
    public int hashCode() {

        return Objects.hash(tempname, clobs, savedate, descs);
    }
}
