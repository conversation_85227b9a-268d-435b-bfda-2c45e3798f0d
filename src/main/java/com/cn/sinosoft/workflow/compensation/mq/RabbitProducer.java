package com.cn.sinosoft.workflow.compensation.mq;

import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */
@Component
public class RabbitProducer {

    @Autowired
    private AmqpTemplate rabbitTemplate;
    @Autowired
    private SimpleRabbitListenerContainerFactory customContainerFactory;
    //发送正常队列
    public void sendPolicyQueue(String json, Map<String ,String> map) {
        MessagePostProcessor header = new MessagePostProcessor() {
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setHeader("retryTime",map.get("retryTime"));
                message.getMessageProperties().setHeader("workFlowId",map.get("workFlowId"));
                message.getMessageProperties().setHeader("serviceId",map.get("serviceId"));
                return message;
            }
        };
        this.rabbitTemplate.convertAndSend(MqConstant.RETRY_EXCHANGE2, MqConstant.NORMAL_ROUTE2, json,header);
    }
    //发送死信队列
    public void sendPolicyQueueDead(String json) {
        this.rabbitTemplate.convertAndSend(MqConstant.DEAD_EXCHANGE2,MqConstant.DEAD_ROUTE2, json);
    }

    public void sendHighLoadQueue(String json, String serviceId) {
        MessagePostProcessor header = new MessagePostProcessor() {
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setHeader("serviceId", serviceId);
                return message;
            }
        };
        this.rabbitTemplate.convertAndSend(MqConstant.HIGH_LOAD_EXCHANGE, MqConstant.HIGH_LOAD_NORMAL_ROUTE, json, header);
    }


}
