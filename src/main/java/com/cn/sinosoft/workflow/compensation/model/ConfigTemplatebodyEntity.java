package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.*;
import java.util.Objects;

/**
 * 模板详情信息
 */
@Entity
@Table(name = "CONFIG_TEMPLATEBODY")
public class ConfigTemplatebodyEntity {
    //模板ID 格式001001001
    private ConfigTempaltebodyIdEntity id;
    //分组等级 1、2、3等
    private long groupingnumber;
    //一级分组（分组内部调用必选）
    private String grouping1;
    //二级分组
    private String grouping2;
    //三级分组
    private String grouping3;
    //四级分组
    private String grouping4;
    //五级分组
    private String grouping5;
    //六级分组
    private String grouping6;
    //参数代码
    private String parameterid;
    //是否必选 Y必选N非必选S选填
    private String mandatory;
    //注释
    private String annotation;
    //返回值成功标志 0否1是
    private String successflag;
    //返回值正确标示
    private String judge;
    //备注
    private String descs;

    private String containjudge;

    private String format;

    @EmbeddedId
    public ConfigTempaltebodyIdEntity getId() {
        return id;
    }

    public void setId(ConfigTempaltebodyIdEntity id) {
        this.id = id;
    }

    @Basic
    @Column(name = "GROUPINGNUMBER")
    public long getGroupingnumber() {
        return groupingnumber;
    }

    public void setGroupingnumber(long groupingnumber) {
        this.groupingnumber = groupingnumber;
    }

    @Basic
    @Column(name = "GROUPING1")
    public String getGrouping1() {
        return grouping1;
    }

    public void setGrouping1(String grouping1) {
        this.grouping1 = grouping1;
    }

    @Basic
    @Column(name = "GROUPING2")
    public String getGrouping2() {
        return grouping2;
    }

    public void setGrouping2(String grouping2) {
        this.grouping2 = grouping2;
    }

    @Basic
    @Column(name = "GROUPING3")
    public String getGrouping3() {
        return grouping3;
    }

    public void setGrouping3(String grouping3) {
        this.grouping3 = grouping3;
    }

    @Basic
    @Column(name = "GROUPING4")
    public String getGrouping4() {
        return grouping4;
    }

    public void setGrouping4(String grouping4) {
        this.grouping4 = grouping4;
    }

    @Basic
    @Column(name = "GROUPING5")
    public String getGrouping5() {
        return grouping5;
    }

    public void setGrouping5(String grouping5) {
        this.grouping5 = grouping5;
    }

    @Basic
    @Column(name = "GROUPING6")
    public String getGrouping6() {
        return grouping6;
    }

    public void setGrouping6(String grouping6) {
        this.grouping6 = grouping6;
    }

    @Basic
    @Column(name = "PARAMETERID")
    public String getParameterid() {
        return parameterid;
    }

    public void setParameterid(String parameterid) {
        this.parameterid = parameterid;
    }

    @Basic
    @Column(name = "MANDATORY")
    public String getMandatory() {
        return mandatory;
    }

    public void setMandatory(String mandatory) {
        this.mandatory = mandatory;
    }

    @Basic
    @Column(name = "ANNOTATION")
    public String getAnnotation() {
        return annotation;
    }

    public void setAnnotation(String annotation) {
        this.annotation = annotation;
    }

    @Basic
    @Column(name = "SUCCESSFLAG")
    public String getSuccessflag() {
        return successflag;
    }

    public void setSuccessflag(String successflag) {
        this.successflag = successflag;
    }

    @Basic
    @Column(name = "JUDGE")
    public String getJudge() {
        return judge;
    }

    public void setJudge(String judge) {
        this.judge = judge;
    }

    @Basic
    @Column(name = "DESCS")
    public String getDescs() {
        return descs;
    }

    public void setDescs(String descs) {
        this.descs = descs;
    }

    @Basic
    @Column(name = "CONTAINJUDGE")
    public String getContainjudge() {
        return containjudge;
    }

    public void setContainjudge(String containjudge) {
        this.containjudge = containjudge;
    }

    @Basic
    @Column(name = "FORMAT")
    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfigTemplatebodyEntity that = (ConfigTemplatebodyEntity) o;
        return id == that.id &&
                groupingnumber == that.groupingnumber &&
                Objects.equals(grouping1, that.grouping1) &&
                Objects.equals(grouping2, that.grouping2) &&
                Objects.equals(grouping3, that.grouping3) &&
                Objects.equals(grouping4, that.grouping4) &&
                Objects.equals(grouping5, that.grouping5) &&
                Objects.equals(grouping6, that.grouping6) &&
                Objects.equals(parameterid, that.parameterid) &&
                Objects.equals(mandatory, that.mandatory) &&
                Objects.equals(annotation, that.annotation) &&
                Objects.equals(successflag, that.successflag) &&
                Objects.equals(judge, that.judge) &&
                Objects.equals(containjudge, that.containjudge) &&
                Objects.equals(format, that.format) &&
                Objects.equals(descs, that.descs);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, groupingnumber, grouping1, grouping2, grouping3, grouping4, grouping5, grouping6, parameterid, mandatory, annotation, successflag, judge,containjudge,format, descs);
    }
}
