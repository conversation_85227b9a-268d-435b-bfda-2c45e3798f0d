package com.cn.sinosoft.workflow.compensation.util;

import com.cn.sinosoft.workflow.compensation.common.Common;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.*;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.*;
import java.util.Map;

/**
 * describe:
 * remark:
 * author: 韩盟凯
 * Date: 2017-12-7 9:07
 */
public class HttpClient {

    private static Logger log = LogManager.getLogger(HttpClient.class);

    /**
     * 发送post请求
     *
     * @param url   地址
     * @param param 参数
     * @return 返回值
     * @throws IOException 异常
     */
    public static String sendPost(String url, String param) throws IOException {
        log.info("----------------------------------------------开始请求-----------------------------");
        String result = "";
        URL realUrl = new URL(url);
        // 打开和URL之间的连接
        URLConnection conn = realUrl.openConnection();
        // 设置通用的请求属性
        conn.setRequestProperty("accept", "*/*");
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        // 发送POST请求必须设置如下两行
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setRequestProperty("Accept-Charset", "UTF-8");
        conn.setRequestProperty("contentType", "UTF-8");
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setConnectTimeout(300000);
        conn.setReadTimeout(300000);
        // 获取URLConnection对象对应的输出流
        OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");
        log.info("request===>>>" + param);
        out.write(param);
        // flush输出流的缓冲
        out.flush();
        // 定义BufferedReader输入流来读取URL的响应
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
        String line;
        log.info("=============================");
        log.info("Contents of post request");
        log.info("=============================");
        while ((line = in.readLine()) != null) {
            result += line;
        }
        log.info("=============================");
        log.info("Contents of post request ends");
        log.info("=============================");
        //关闭输出流、输入流
        out.close();
        in.close();
        log.info("response===>>>" + result);
        log.info("----------------------------------------------请求结束-----------------------------");
        return result;
    }

    /**
     * post请求（用于请求json格式的参数）
     *
     * @param url
     * @param param
     * @return
     */
    public static String doPosting(String url, String param) throws Exception {
        log.info("----------------------------------------------开始请求-----------------------------");
        String result = "";
        URL realUrl = new URL(url);
        // 打开和URL之间的连接
        HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
        // 设置通用的请求属性
        conn.setRequestProperty("accept", "*/*");
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        // 发送POST请求必须设置如下两行
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Accept-Charset", "UTF-8");
        conn.setRequestProperty("contentType", "UTF-8");
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setConnectTimeout(300000);
        conn.setReadTimeout(300000);
        // 获取URLConnection对象对应的输出流
        OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");
        log.info("request===>>>" + param);
        out.write(param);
        // flush输出流的缓冲
        out.flush();
        // 定义BufferedReader输入流来读取URL的响应
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
        String line;
        log.info("=============================");
        log.info("Contents of post request");
        log.info("=============================");
        while ((line = in.readLine()) != null) {
            result += line;
        }
        log.info("=============================");
        log.info("Contents of post request ends");
        log.info("=============================");
        //关闭输出流、输入流
        out.close();
        in.close();
        log.info("response===>>>" + result);
        log.info("----------------------------------------------请求结束-----------------------------");
        return result;
    }


    /**
     * 微信请求方法
     *
     * @param jsonData
     * @param POST_URL
     * @return
     * @throws IOException
     */
    public static String readContentFromPost(String jsonData, String POST_URL) {
        // Post请求的url，与get不同的是不需要带参数
        URL postUrl = null;
        try {
            postUrl = new URL(POST_URL);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) postUrl.openConnection();
            // http正文内，因此需要设为true
            connection.setDoOutput(true);
            // Read from the connection. Default is true.
            connection.setDoInput(true);
            // Set the post method. Default is GET
            connection.setRequestMethod("POST");
            // Post 请求不能使用缓存
            connection.setUseCaches(false);
            log.info("----------------------------------------------开始请求-----------------------------");
            log.info("request===>>>{}", jsonData);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

            connection.setConnectTimeout(360000);
            connection.setReadTimeout(360000);
            connection.connect();
            DataOutputStream out = new DataOutputStream(connection.getOutputStream());
            String content = "jsonData=" + URLEncoder.encode(jsonData, "utf-8");
            out.writeBytes(content);
            out.flush();
            out.close(); // flush and close
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
            String line;
            log.info("=============================");
            log.info("Contents of post request");
            log.info("=============================");
            StringBuilder jsonString = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }
            log.info("=============================");
            log.info("Contents of post request ends");
            log.info("=============================");
            reader.close();
            connection.disconnect();
            log.info("request===>>>{}", jsonString.toString());
            log.info("----------------------------------------------开始结束-----------------------------");
            return jsonString.toString();
        } catch (Exception ex) {
            log.info("readContentFromPost请求失败，失败原因={}", ex.toString());
            Common common = new Common();
            common.setResCode("400");
            common.setResMsg(ex.toString());
            return errorHttpRes(common);

        }
    }

    /**
     * 反洗钱调用方法
     *
     * @param host
     * @param packet
     * @return
     * @throws Exception
     */
    public static String send(String host, String packet) throws Exception {

        String[] strArr = host.split(":");
        TcpClient2 client = new TcpClient2();
        client.setEncoding("GBK");
        client.setHeadBeforeLength("");
        client.setHeadAfterLength(StringUtils.rightPad("", 7) + "gs0007");
        client.setLengthHeadSize(6);
        client.setLengthHeadPad(0);
        client.setLengthHeadPadChar(" ");
        String response = client.call(strArr[0], Integer.parseInt(strArr[1]), packet);
        return response;
    }

    public static String doPostString(String url, String content) {

        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(600000).setConnectionRequestTimeout(600000)
                .setSocketTimeout(600000).build();
        post.setConfig(requestConfig);
        String result = "";
        try {
            StringEntity s = new StringEntity(content, "utf-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("text/plain");//发送json数据需要设置contentType
            System.out.print(s);
            post.setEntity(s);
            HttpResponse res = httpclient.execute(post);
            if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                result = EntityUtils.toString(res.getEntity());// 返回json格式：
            }
        } catch (Exception e) {
//            throw new RuntimeException(e);
            log.info("\n2请求失败，失败原因=" + e.toString());
            //ex.printStackTrace();
            Common common = new Common();
            common.setResCode("400");
            common.setResMsg(e.toString());
            return errorHttpRes(common);
        }
        return result;
    }

    public static String doPost(String sendStr, String url) {
        HttpEntityEnclosingRequestBase httpMethod = null;

        try {
            DefaultHttpClient httpClient = new DefaultHttpClient();
            httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 60 * 10000);
            httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 60 * 10000);
            httpClient.setHttpRequestRetryHandler(new DefaultHttpRequestRetryHandler());
            SSLContext sslcontext = SSLContext.getInstance("TLS");
            sslcontext.init(null, null, null);
            SSLSocketFactory sf = new SSLSocketFactory(sslcontext, SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            Scheme sch = new Scheme("https", 443, sf);
            httpClient.getConnectionManager().getSchemeRegistry().register(sch);
            httpClient.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy() {
                @Override
                public long getKeepAliveDuration(HttpResponse response,
                                                 HttpContext context) {
                    long keepAlive = super.getKeepAliveDuration(response, context);
                    if (keepAlive == -1) {
                        // 如果keep-alive值没有由服务器明确设置，那么保持连接持续5秒。
                        keepAlive = 600000;
                    }
                    return keepAlive;
                }
            });
            httpMethod = new HttpPost(url);
            String defaultEncode = "UTF-8";

            ByteArrayEntity reqEntity = new ByteArrayEntity(
                    sendStr.getBytes(defaultEncode));
            reqEntity.setContentType("application/xml");
            httpMethod.setHeader("Content-Type", "application/xml;charset="
                    + defaultEncode);
            httpMethod.setEntity(reqEntity);

            HttpResponse httpResponse = httpClient.execute(httpMethod);
            String responseStr = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            return responseStr;
        } catch (Exception ex) {
            log.info("\n2请求失败，失败原因=" + ex.toString());
            //ex.printStackTrace();
            Common common = new Common();
            common.setResCode("400");
            common.setResMsg(ex.toString());
            return errorHttpRes(common);

        }
        //return null;
    }

    public static String call(String path, String requestXML, String encode) throws Exception {

        URL url = new URL(path);
        System.out.println("---访问路径---" + path);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");// 提交模式
        conn.setConnectTimeout(200000);// 连接超时 单位毫秒
        conn.setRequestProperty("Content-type", "application/xml");
        conn.setReadTimeout(200000);// 读取超时 单位毫秒
        conn.setDoOutput(true);// 是否输入参数
        // 通过conn.getOutputStream().write 将XML信息写入，在另一端系统，get出来再解析
        conn.getOutputStream().write(requestXML.getBytes(encode));
        BufferedReader reader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), encode));
        StringBuilder buffer = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null) {
            buffer.append(line).append("\n");
        }
        return buffer.toString();
    }


    public static String PostString(String url, String content) {

        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(600000).setConnectionRequestTimeout(600000)
                .setSocketTimeout(600000).build();
        post.setConfig(requestConfig);
        String result = "";
        String decode = "";
        try {
            StringEntity s = new StringEntity(content, "utf-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            System.out.print(s);
            post.setEntity(s);
            HttpResponse res = httpclient.execute(post);
            if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                decode = URLDecoder.decode(String.valueOf(EntityUtils.toString(res.getEntity())), "UTF-8");
                //result = EntityUtils.toString(res.getEntity());// 返回json格式：
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return decode;
    }

    public static String doPostWithHead(String sendStr, String url, Map<String,String> head) {
        HttpEntityEnclosingRequestBase httpMethod = null;
        try {
            DefaultHttpClient httpClient = new DefaultHttpClient();
            httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 60 * 10000);
            httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 60 * 10000);
            httpClient.setHttpRequestRetryHandler(new DefaultHttpRequestRetryHandler());
            SSLContext sslcontext = SSLContext.getInstance("TLS");
            sslcontext.init(null, null, null);
            SSLSocketFactory sf = new SSLSocketFactory(sslcontext, SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            Scheme sch = new Scheme("https", 443, sf);
            httpClient.getConnectionManager().getSchemeRegistry().register(sch);
            httpClient.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy() {
                @Override
                public long getKeepAliveDuration(HttpResponse response,
                                                 HttpContext context) {
                    long keepAlive = super.getKeepAliveDuration(response, context);
                    if (keepAlive == -1) {
                        // 如果keep-alive值没有由服务器明确设置，那么保持连接持续5秒。
                        keepAlive = 600000;
                    }
                    return keepAlive;
                }
            });
            httpMethod = new HttpPost(url);
            String defaultEncode = "UTF-8";

            ByteArrayEntity reqEntity = new ByteArrayEntity(
                    sendStr.getBytes(defaultEncode));
            reqEntity.setContentType("application/xml");
            httpMethod.setHeader("Content-Type", "application/xml;charset="
                    + defaultEncode);
            httpMethod.setEntity(reqEntity);
            httpMethod.setHeader("workFlowId",head.get("workFlowId"));
            httpMethod.setHeader("serviceId",head.get("serviceId"));
            httpMethod.setHeader("retryTime",head.get("retryTime"));
            HttpResponse httpResponse = httpClient.execute(httpMethod);
            String responseStr = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            return responseStr;
        } catch (Exception ex) {
            log.info("\n2请求失败，失败原因=" + ex.toString());
            //ex.printStackTrace();
            Common common = new Common();
            common.setResCode("400");
            common.setResMsg(ex.toString());
            return errorHttpRes(common);

        }
        //return null;
    }

    /**
     * http请求统一处理得到的错误报文方法
     *
     * @return
     */
    private static String errorHttpRes(Common common) {
        JSONObject res = new JSONObject();
        JSONObject data_res = new JSONObject();
        JSONObject comm = new JSONObject(common);
        data_res.put("common", comm);
        res.put("data", data_res);
        return res.toString();
    }
}