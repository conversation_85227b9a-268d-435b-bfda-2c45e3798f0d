package com.cn.sinosoft.workflow.compensation.util;

import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;

public class Cxf {

    /**
     * CXF请求
     *
     * @return
     */
    public static String cxfGo(String url,String serverName,String dataXML) throws Exception {

        JaxWsDynamicClientFactory factory = JaxWsDynamicClientFactory.newInstance();
        Client client = factory.createClient(url);
        Object[] res = null;
        if(serverName.contains(",")){
            String[] strings = serverName.split(",");
            res = client.invoke(strings[0], new Object[]{strings[1],dataXML});
        }else {
            res = client.invoke(serverName, new Object[]{dataXML});
        }
        return res[0].toString();
    }
}
