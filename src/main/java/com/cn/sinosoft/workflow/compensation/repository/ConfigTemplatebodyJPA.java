package com.cn.sinosoft.workflow.compensation.repository;

import com.cn.sinosoft.workflow.compensation.model.ConfigTemplatebodyEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ConfigTemplatebodyJPA extends JpaRepository<ConfigTemplatebodyEntity,String> {

    //根据主键查询
    @Query(value = "select d from ConfigTemplatebodyEntity d where d.id.templateid=?1 and d.mandatory='Y'")
    List<ConfigTemplatebodyEntity> findAllByTemplateid(String templateid);
}
