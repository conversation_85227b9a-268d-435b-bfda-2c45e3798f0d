package com.cn.sinosoft.workflow.compensation.util;

import com.cn.sinosoft.workflow.compensation.common.Common;
import com.cn.sinosoft.workflow.compensation.model.ConfigTemplatebodyEntity;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * xml处理方法
 */
public  class XmlUtrl {

    /**
     * xml取值
     * @return 结果
     */
    public static Common getParameterid(ConfigTemplatebodyEntity entity, String res){

        Common common = new Common();
        Document document = null;
        try {
            document = DocumentHelper.parseText(res);
        } catch (DocumentException e) {
            common.setResCode("00");
            common.setResMsg(entity.getParameterid()+"不能识别的报文格式");
        }
        Element root = document.getRootElement();
        String resTemp="";

        List<Iterator<Element>> iteratorList = new ArrayList<>();

        if(entity.getGroupingnumber()>0){
            List<Iterator<Element>> iterators = new ArrayList<>();
            for (int i = 0;i<entity.getGroupingnumber();i++){
                if (i==0){
                    Iterator<Element> iterator = root.elementIterator(entity.getGrouping1());
                    if (iterator!=null) {
                        iterators.add(iterator);
                        iteratorList.addAll(iterators);
                    }
                }else{
                    iterators.clear();
                    if (iteratorList.size()!=0) {
                        for (Iterator<Element> iterator:iteratorList) {
                            while (iterator.hasNext()){
                                Element elementGroupService =iterator.next();
                                String getter = "getGrouping" + (i + 1);
                                try {
                                    Method method = entity.getClass().getMethod(getter, new Class[]{});
                                    String value = method.invoke(entity, new Object[]{}).toString();
                                    Iterator<Element> it = elementGroupService.elementIterator(value);
                                    iterators.add(it);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                    iteratorList.clear();
                    iteratorList.addAll(iterators);
                }
            }
        }
        List<Object> objects = new ArrayList<>();
        if (entity.getGroupingnumber()<=0){
            JSONObject jsonObject = fase(resTemp,entity,root,common);
            if (jsonObject!=null) {
                objects.add(jsonObject);
            }
        }else {
            if (iteratorList.size() != 0) {
                for (Iterator<Element> iterator : iteratorList) {
                    while (iterator.hasNext()) {

                        Element iteratorElement = iterator.next();
                        JSONObject jsonObject = fase(resTemp, entity, iteratorElement, common);
                        if (jsonObject != null) {
                            objects.add(jsonObject);
                        }
                    }
                }
            }
        }

//
//        if(entity.getGroupingnumber()==0){
//            Element par = root.element(entity.getParameterid());
//            resTemp = par.getText();
//        }else if(entity.getGroupingnumber()==1){
//            Element pathEl = root.element(entity.getGrouping1());
//            Element par1 = pathEl.element(entity.getParameterid());
//            resTemp = par1.getText();
//        }else if(entity.getGroupingnumber()==2){
//            Element pathEl = root.element(entity.getGrouping1());
//            Element par1 = pathEl.element(entity.getGrouping2());
//            Element par2 = par1.element(entity.getParameterid());
//            resTemp = par2.getText();
//        }else if(entity.getGroupingnumber()==3){
//            Element pathEl = root.element(entity.getGrouping1());
//            Element par1 = pathEl.element(entity.getGrouping2());
//            Element par2 = par1.element(entity.getGrouping3());
//            Element par3 = par2.element(entity.getParameterid());
//            resTemp = par3.getText();
//        }else if(entity.getGroupingnumber()==4){
//            Element pathEl = root.element(entity.getGrouping1());
//            Element par1 = pathEl.element(entity.getGrouping2());
//            Element par2 = par1.element(entity.getGrouping3());
//            Element par3 = par2.element(entity.getGrouping4());
//            Element par4 = par3.element(entity.getParameterid());
//            resTemp = par4.getText();
//        }else if(entity.getGroupingnumber()==5){
//            Element pathEl = root.element(entity.getGrouping1());
//            Element par1 = pathEl.element(entity.getGrouping2());
//            Element par2 = par1.element(entity.getGrouping3());
//            Element par3 = par2.element(entity.getGrouping4());
//            Element par4 = par3.element(entity.getGrouping5());
//            Element par5 = par4.element(entity.getParameterid());
//            resTemp = par5.getText();
//        }
//        else if(entity.getGroupingnumber()==6){
//            Element pathEl = root.element(entity.getGrouping1());
//            Element par1 = pathEl.element(entity.getParameterid());
//            Element par2 = par1.element(entity.getGrouping3());
//            Element par3 = par2.element(entity.getGrouping4());
//            Element par4 = par3.element(entity.getGrouping5());
//            Element par5 = par4.element(entity.getGrouping6());
//            Element par6 = par5.element(entity.getParameterid());
//            resTemp = par6.getText();
//        }
        if (objects.size()!=0){
            String[] strArr = entity.getParameterid().split("&");
            if (strArr.length>1){
                common.setResCode("011");
                common.setResMsg(new JSONObject().put(entity.getAnnotation(),objects).toString());
            }else{
                common.setResCode("01");
                common.setResMsg(((JSONObject)objects.get(0)).getString(entity.getParameterid()));
            }
        }else{

            common.setResCode("00");
            common.setResMsg("获取失败，失败原因："+common.getResMsg()+"或数据为空");
        }
        return common;
    }

   private static JSONObject fase(String resTemp,ConfigTemplatebodyEntity entity,Element iteratorElement,Common common){
       String[] strArr = entity.getParameterid().split("&");
       JSONObject jsonObject =new JSONObject();
       for (String s : strArr) {
           Element parament = iteratorElement.element(s);
           if (parament != null) {
               resTemp = parament.getText();
           }
           if (entity.getSuccessflag().equals("1")) {
               if ("not null".equals(entity.getJudge())) {
                   if (resTemp == null || "".equals(resTemp) || "null".equals(resTemp)) {
                       common.setResCode("00");
                       common.setResMsg(entity.getParameterid() + "字段获取失败，调用无效");
                       return null;
                   } else {
                       if (entity.getContainjudge() != null && !entity.getContainjudge().equals("")) {
                           String[] strings = entity.getContainjudge().split("&");
                           boolean b = false;
                           for (int a = 0; a < strings.length; a++) {
                               if (resTemp.indexOf(strings[a]) != -1) {
                                   b = true;
                               }
                           }
                           if (b) {
                               jsonObject.put(s, "1");
                           } else {
                               jsonObject.put(s, "0");
                           }
                       } else {
                           jsonObject.put(s, resTemp);
                       }
                   }
               } else {
                   if (entity.getJudge() != null && !resTemp.equals(entity.getJudge())) {
                       common.setResCode("00");
                       common.setResMsg(entity.getParameterid() + "内容应该是" + resTemp + "\n");
                       return null;
                   } else {
                       if (entity.getContainjudge() != null && !entity.getContainjudge().equals("")) {
                           String[] strings = entity.getContainjudge().split("&");
                           boolean b = false;
                           for (int a = 0; a < strings.length; a++) {
                               if (resTemp.indexOf(strings[a]) != -1) {
                                   b = true;
                               }
                           }
                           if (b) {
                               jsonObject.put(s, "1");
                           } else {
                               jsonObject.put(s, "0");
                           }
                       } else {
                           jsonObject.put(s, resTemp);
                       }
                   }
               }
           } else {
               jsonObject.put(s, resTemp);
           }
       }
       return jsonObject;
    }
}
