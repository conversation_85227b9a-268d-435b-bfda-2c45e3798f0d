package com.cn.sinosoft.workflow.compensation.controller;

import com.cn.sinosoft.workflow.compensation.mq.RabbitProducer;
import com.cn.sinosoft.workflow.compensation.service.ChannelRetryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */

@Controller
public class ChannelController {

    private static Logger log = LoggerFactory.getLogger(ChannelController.class);

    @Autowired
    ChannelRetryService channelRetryService;
    @Autowired
    RabbitProducer rabbitProducer;

    /**
     * 接口调用异常重试
     * @param data
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "retry", method = {RequestMethod.POST, RequestMethod.GET})
    public void retry(@Param("data") String data , @Param("body") String body, @RequestBody String requestBody,HttpServletRequest request) {
        log.info("接口调用异常重试开始");
        if(data==null || "".equals(data)){
            if(body!=null&&!"".equals(body)){
                data = body;
            }else{
                try {
                    data = requestBody;

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        log.info("接口调用异常重试入参data:{}", data);
        Map<String ,String > map = new HashMap<>();
        map.put("workFlowId",request.getHeader("workFlowId"));//流id
        map.put("serviceId",request.getHeader("serviceId"));//流内的serverid
        map.put("retryTime",request.getHeader("retryTime"));//重试次数
        channelRetryService.compensationRetry(data,map);
        log.info("接口调用异常重试完成");
    }

    /**
     * 高并发业务生产者队列
     * @param data
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "sendHighLoadQueue", method = {RequestMethod.POST, RequestMethod.GET})
    public void sendHighLoadQueue(@Param("data") String data , @Param("body") String body,
                                  @RequestBody String requestBody,HttpServletRequest request) {
        if(StringUtils.isEmpty(data)){
            if(!StringUtils.isEmpty(body)){
                data = body;
            }else{
                data = requestBody;
            }
        }
        log.info("高并发业务生产者队列：{}", data);
        String serviceId = request.getHeader("serviceId");
        rabbitProducer.sendHighLoadQueue(data, serviceId);
        log.info("高并发业务生产者队列：{}处理完成", data);
    }
}

