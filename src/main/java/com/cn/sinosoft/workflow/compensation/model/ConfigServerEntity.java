package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * 服务记录表
 */
@Entity
@Table(name = "CONFIG_SERVER")
public class ConfigServerEntity {
    //服务ID 格式001001
    private String serviceid;
    //服务名称
    private String servicename;
    //归属系统ID
    private String systemid;
    //版本号
    private String version;
    //调用方式
    private String calltype;
    //调用地址
    private String urlpath;
    //报文格式
    private String type;
    //系统上线时间
    private Date onlinedate;
    //下线时间
    private Date enddate;
    //系统状态0：正常1：半工2：下线
    private String flag;
    //备注
    private String descs;

    @Id
    @Column(name = "SERVICEID")
    public String getServiceid() {
        return serviceid;
    }

    public void setServiceid(String serviceid) {
        this.serviceid = serviceid;
    }

    @Basic
    @Column(name = "SERVICENAME")
    public String getServicename() {
        return servicename;
    }

    public void setServicename(String servicename) {
        this.servicename = servicename;
    }

    @Basic
    @Column(name = "SYSTEMID")
    public String getSystemid() {
        return systemid;
    }

    public void setSystemid(String systemid) {
        this.systemid = systemid;
    }

    @Basic
    @Column(name = "VERSION")
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Basic
    @Column(name = "CALLTYPE")
    public String getCalltype() {
        return calltype;
    }

    public void setCalltype(String calltype) {
        this.calltype = calltype;
    }

    @Basic
    @Column(name = "URLPATH")
    public String getUrlpath() {
        return urlpath;
    }

    public void setUrlpath(String urlpath) {
        this.urlpath = urlpath;
    }

    @Basic
    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Basic
    @Column(name = "ONLINEDATE")
    public Date getOnlinedate() {
        return onlinedate;
    }

    public void setOnlinedate(Date onlinedate) {
        this.onlinedate = onlinedate;
    }

    @Basic
    @Column(name = "ENDDATE")
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    @Basic
    @Column(name = "FLAG")
    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    @Basic
    @Column(name = "DESCS")
    public String getDescs() {
        return descs;
    }

    public void setDescs(String descs) {
        this.descs = descs;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfigServerEntity that = (ConfigServerEntity) o;
        return Objects.equals(serviceid, that.serviceid) &&
                Objects.equals(servicename, that.servicename) &&
                Objects.equals(systemid, that.systemid) &&
                Objects.equals(version, that.version) &&
                Objects.equals(calltype, that.calltype) &&
                Objects.equals(urlpath, that.urlpath) &&
                Objects.equals(onlinedate, that.onlinedate) &&
                Objects.equals(enddate, that.enddate) &&
                Objects.equals(flag, that.flag) &&
                Objects.equals(descs, that.descs);
    }

    @Override
    public int hashCode() {

        return Objects.hash(serviceid, servicename, systemid, version, calltype, urlpath, onlinedate, enddate, flag, descs);
    }
}
