package com.cn.sinosoft.workflow.compensation.util;

import com.cn.sinosoft.workflow.compensation.model.ConfigTempLateDataEntity;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.StringWriter;
import java.util.Map;

/**
 * Freemarker模板工具类
 */
public class FreemarkerUtil {

	private static Logger log =  LogManager.getLogger(FreemarkerUtil.class);
	/**
	 * 组装报文
	 * @param d 模板对象
	 * @param map 入参map
	 * @return 报文实体
	 * @throws Exception
	 */
	public static String setTemplate(ConfigTempLateDataEntity d, Map<String, Object> map) throws Exception {
		Configuration cfg = new Configuration();
		StringTemplateLoader stringLoader = new StringTemplateLoader();
		String templateContent=d.getClobs();
		stringLoader.putTemplate("myTemplate",templateContent);

		cfg.setTemplateLoader(stringLoader);
		Template template = cfg.getTemplate("myTemplate","utf-8");

		StringWriter writer = new StringWriter();
		template.process(map, writer);
		return writer.toString();
	}
}
