package com.cn.sinosoft.workflow.compensation.common;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */
public class Common {
    //返回状态 0失败 1成功
    private String resCode = "01";
    //返回信息 成功 或 失败信息
    private String resMsg = "成功";

    /**
     * 恒丰专用字段，返回参数信息，
     * zyh 20230421
     */
//    private List<EvergrowingReturnErrorsDto> errors;
//
//    public List<EvergrowingReturnErrorsDto> getErrors() {
//        return errors;
//    }
//
//    public void setErrors(List<EvergrowingReturnErrorsDto> errors) {
//        this.errors = errors;
//    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }
}
