package com.cn.sinosoft.workflow.compensation.util;

import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */
public class RabbitMqUtil {
    private static ConnectionFactory factory;

    public static Connection getConnection() throws IOException, TimeoutException {
        //创建mq的连接工厂对象
        factory = new ConnectionFactory();
        //设置连接rabbit的主机地址
        factory.setHost("");
        //设置连接端口
        factory.setPort(5672);
        //设置连接哪个虚拟主机
        factory.setVirtualHost("host1");
        //设置虚拟主机的用户名和密码
        factory.setUsername("admin");
        factory.setPassword("password");
        Connection connection = factory.newConnection();
        return connection;
    }
}
