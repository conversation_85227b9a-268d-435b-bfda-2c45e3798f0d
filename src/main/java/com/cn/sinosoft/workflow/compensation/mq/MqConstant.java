package com.cn.sinosoft.workflow.compensation.mq;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */
public interface MqConstant {
    //交换机 正常和死信交换机
    String RETRY_EXCHANGE ="compensation.retry.exchange";
    String DEAD_EXCHANGE="compensationRetry.dead.exchange";

    //队列 正常和死信队列
    String RETRY_NORMAL_QUEUE ="retry.normal.queue";
    String ELE_WRONG_NORMAL_QUEUE="robot.elepolicy.wrong.normal.queue";
    String RETRY_DEAD_QUEUE="retry.dead.queue";

    //route key 正常交换机的bindingKey 死信交换机的bindingKey
    String NORMAL_ROUTE="robot.normal.route";// 这里充当route key
    String DEAD_ROUTE="robot.dead.route";

    // 过期时间设置 3分钟
    Long TTL_TIME=120000L;


    //交换机 正常和死信交换机(发送)
    String RETRY_EXCHANGE2 ="WX_delay_temp_exchange";
    //交换机 死信交换机(超出重试次数的)
    String DEAD_EXCHANGE2 ="WX_delay_real_deay_queue";
    //队列 正常和死信队列
    String RETRY_NORMAL_QUEUE2 ="WX_delay_consumer_queue";

    //route key 正常交换机的bindingKey 死信交换机的bindingKey
    String NORMAL_ROUTE2="route_key";// 这里充当route key
    String DEAD_ROUTE2="route_key";


    //交换机 正常和死信交换机(发送)
    String HIGH_LOAD_EXCHANGE ="WX_high_load_exchange";
    //交换机 死信交换机(超出重试次数的)
    String HIGH_LOAD_DEAD_EXCHANGE ="WX_high_load_dead_exchange";
    //队列 正常队列
    String HIGH_LOAD_NORMAL_QUEUE ="WX_high_load_queue";
    //队列 死信队列
    String HIGH_LOAD_DEAD_QUEUE ="WX_high_load_dead_queue";
    //route key 正常交换机的bindingKey 死信交换机的bindingKey
    String HIGH_LOAD_NORMAL_ROUTE="high_load_route_key";// 这里充当route key
    String HIGH_LOAD_DEAD_ROUTE="high_load_route_key";
}
