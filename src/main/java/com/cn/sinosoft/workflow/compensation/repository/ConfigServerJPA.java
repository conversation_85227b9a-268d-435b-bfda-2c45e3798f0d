package com.cn.sinosoft.workflow.compensation.repository;

import com.cn.sinosoft.workflow.compensation.model.ConfigServerEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ConfigServerJPA extends JpaRepository<ConfigServerEntity,String> {

    //根据主键查询
    @Query(value = "select d from ConfigServerEntity d where d.serviceid=?1 and (d.flag ='0' or d.flag='1')")
    ConfigServerEntity findAllByServiceid(String serviceid);
}
