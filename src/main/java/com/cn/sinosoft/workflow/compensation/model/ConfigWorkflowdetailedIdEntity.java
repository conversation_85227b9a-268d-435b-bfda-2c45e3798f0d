package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class ConfigWorkflowdetailedIdEntity implements Serializable {

    //工作流ID
    private String workflowid;
    //流程排序
    private long serialno;

    @Column(name = "WORKFLOWID")
    public String getWorkflowid() {
        return workflowid;
    }

    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    @Column(name = "SERIALNO")
    public long getSerialno() {
        return serialno;
    }

    public void setSerialno(long serialno) {
        this.serialno = serialno;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfigWorkflowdetailedIdEntity that = (ConfigWorkflowdetailedIdEntity) o;
        return serialno == that.serialno &&
                Objects.equals(workflowid, that.workflowid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workflowid, serialno);
    }
}
