package com.cn.sinosoft.workflow.compensation.repository;

import com.cn.sinosoft.workflow.compensation.model.ConfigWorkflowdetailedEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ConfigWorkflowdetailedJPA extends JpaRepository<ConfigWorkflowdetailedEntity,String> {

    //查询所在流的对应接口
    @Query(value = "select d from ConfigWorkflowdetailedEntity d where d.id.workflowid=?1 and d.serviceid=?2  ")
    ConfigWorkflowdetailedEntity findByWorkflowidAndServiceId(String workFlowId, String serviceId);
}
