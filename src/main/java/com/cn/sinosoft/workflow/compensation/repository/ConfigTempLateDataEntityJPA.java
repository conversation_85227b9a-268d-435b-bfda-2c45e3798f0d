package com.cn.sinosoft.workflow.compensation.repository;

import com.cn.sinosoft.workflow.compensation.model.ConfigTempLateDataEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ConfigTempLateDataEntityJPA extends JpaRepository<ConfigTempLateDataEntity,String> {

    @Query(value = "SELECT * FROM CONFIG_TEMPLATEDATA WHERE  TEMPNAME=?1",nativeQuery = true)
    ConfigTempLateDataEntity findByName(String tempName);
}
