package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Embeddable
public class ConfigTempaltebodyIdEntity implements Serializable {

    //模板ID 格式001001001
    private String templateid;
    //排序
    private long exhibition;

    @Column(name = "TEMPLATEID")
    public String getTemplateid() {
        return templateid;
    }

    public void setTemplateid(String templateid) {
        this.templateid = templateid;
    }

    @Column(name = "EXHIBITION")
    public long getExhibition() {
        return exhibition;
    }

    public void setExhibition(long exhibition) {
        this.exhibition = exhibition;
    }
}
