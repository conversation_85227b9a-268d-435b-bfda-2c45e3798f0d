package com.cn.sinosoft.workflow.compensation.repository;

import com.cn.sinosoft.workflow.compensation.model.ConfigTemplateEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ConfigTemplateJPA extends JpaRepository<ConfigTemplateEntity,String> {

    //根据主键查询
    @Query(value = "select d from ConfigTemplateEntity d where d.templateid=?1")
    ConfigTemplateEntity findAllByTemplateid(String templateid);
}
