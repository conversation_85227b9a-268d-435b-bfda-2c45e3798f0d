package com.cn.sinosoft.workflow.compensation.util;

import com.cn.sinosoft.workflow.compensation.common.Common;
import com.cn.sinosoft.workflow.compensation.model.ConfigTempLateDataEntity;
import com.cn.sinosoft.workflow.compensation.model.ConfigTemplateEntity;
import com.cn.sinosoft.workflow.compensation.model.ConfigTemplatebodyEntity;
import com.cn.sinosoft.workflow.compensation.repository.ConfigTempLateDataEntityJPA;
import com.cn.sinosoft.workflow.compensation.repository.ConfigTemplateJPA;
import com.cn.sinosoft.workflow.compensation.repository.ConfigTemplatebodyJPA;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSON;
import net.sf.json.xml.XMLSerializer;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-08
 * describe
 */
@Slf4j
@Component
public class ConvertToJson {

    @Autowired
    private ConfigTemplateJPA configTemplateJPA;
    @Autowired
    private ConfigTemplatebodyJPA configTemplatebodyJPA;

    @Autowired
    private ConfigTempLateDataEntityJPA configTempLateDataEntityJPA;
    public String requestJSON(String req,String templateId) {

        //全局变量
        boolean b =true;
        ConfigTemplateEntity ct = null;
        List<ConfigTemplatebodyEntity> ctbList = null;
        Common common = new Common();
        String res ="";

        //1.获取所采用的模板
        if(b){
            ct = configTemplateJPA.findAllByTemplateid(templateId);
            if(ct==null){
                b=false;
                common.setResCode("template_00001");
                common.setResMsg(templateId+"模板配置找不到");
            }
        }
        //2.获取模板中定义的必选信息
        if(b){
            ctbList = configTemplatebodyJPA.findAllByTemplateid(templateId);
            if(ctbList==null || ctbList.size()<1){
                b=false;
                common.setResCode("template_00001");
                common.setResMsg(templateId+"模板内容找不到");
            }
        }
        Map<String,Object> mss = new HashMap<>();
        //3. 根据模板详情，获取所有必填字段
        if(b){
            for(ConfigTemplatebodyEntity ctb:ctbList){
                //3.1获取报文格式
                if("xml".equals(ct.getFormat())){
                    Common c = XmlUtrl.getParameterid(ctb,req);
                    if("00".equals(c.getResCode())){
                        b=false;
                        common.setResCode("template_00003");
                        common.setResMsg(templateId+"报文转json失败");
                        break;
                    }else{
                        if (c.getResCode().equals("011")) {
                            Map<String,Object> rs = new JSONObject(c.getResMsg()).toMap();
                            mss.put(ctb.getAnnotation(),rs.get(ctb.getAnnotation()));
                        }else{
                            mss.put(ctb.getAnnotation(), c.getResMsg());
                        }
                    }
                }else if("json".equals(ct.getFormat())){
                    JSONObject json = new JSONObject(req);
                    mss = json.toMap();
                }else if("map".equals(ct.getFormat())){

                }else if("xmlToJson".equals(ct.getFormat())){
                    XMLSerializer xmlSerializer=new XMLSerializer();
                    if(req.startsWith("<?xml")){
                    }else{
                        req="<rootxml>"+req+"</rootxml>";
                    }

                    JSON json = xmlSerializer.read(req);
                    String jsonString = json.toString();
                    jsonString = jsonString.replace("[]","\"\"");
                    if(jsonString.startsWith("[")){
                        jsonString = jsonString.substring(1,jsonString.length());
                    }
                    if (jsonString.endsWith("]")){
                        jsonString = jsonString.substring(0,jsonString.length()-1);
                    }
                    JSONObject jsonObject=new JSONObject(jsonString);
                    mss = jsonObject.toMap();
                }
            }
        }
        //根据模板进行转换
        if(b){
            try {
                //调用转换方法
                ConfigTempLateDataEntity d=configTempLateDataEntityJPA.findByName(templateId);
                /* TODO 以下这两行代码有什么作用？ begin */
                JSONObject jsonObject = new JSONObject(mss);
                System.out.println(jsonObject);
                /* TODO 以上这两行代码有什么作用？ end */
                log.info("\n转换模板id=" + templateId);
                log.info("\n转换模板=" + d.getClobs());
                res = FreemarkerUtil.setTemplate(d,mss);
            } catch (Exception e) {
                e.printStackTrace();
                b=false;
                common.setResCode("template_00002");
                common.setResMsg(templateId+"模板转换失败");
            }
        }

        if (!b){
            res=new JSONObject(common).toString();
        }

        return res;
    }
}
