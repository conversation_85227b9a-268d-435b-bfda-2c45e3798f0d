package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.*;

@Entity
@Table(name = "CONFIG_WORKFLOWDETAILED")
public class ConfigWorkflowdetailedEntity {
    //工作流ID
    private ConfigWorkflowdetailedIdEntity id;
    //系统ID 格式001
    private String systemid;
    //服务ID 格式001001
    private String serviceid;
    //特殊入参
    private String parameter;
    //发送模板ID
    private String reqtemplateid;
    //接收模板ID
    private String restemplateid;
    //加密指定报文
    private String reqencryptionId;
    //验证加密结果
    private String resencryptionId;
    //节点属性 01强制 02可忽视
    private String mandatory;
    //是否融合
    private String fuse;
    //调用类型0及时1异步
    private String type;
    //分组代码
    private String groupNo;
    //返回值校验
    private String istrue;
    private Long timethreshold;
    //备注
    private String descs;

    @EmbeddedId
    public ConfigWorkflowdetailedIdEntity getId() {
        return id;
    }

    public void setId(ConfigWorkflowdetailedIdEntity id) {
        this.id = id;
    }

    @Basic
    @Column(name = "SYSTEMID")
    public String getSystemid() {
        return systemid;
    }

    public void setSystemid(String systemid) {
        this.systemid = systemid;
    }

    @Basic
    @Column(name = "SERVICEID")
    public String getServiceid() {
        return serviceid;
    }

    public void setServiceid(String serviceid) {
        this.serviceid = serviceid;
    }

    @Basic
    @Column(name = "PARAMETER")
    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    @Basic
    @Column(name = "REQTEMPLATEID")
    public String getReqtemplateid() {
        return reqtemplateid;
    }

    public void setReqtemplateid(String reqtemplateid) {
        this.reqtemplateid = reqtemplateid;
    }

    @Basic
    @Column(name = "RESTEMPLATEID")
    public String getRestemplateid() {
        return restemplateid;
    }

    public void setRestemplateid(String restemplateid) {
        this.restemplateid = restemplateid;
    }

    @Basic
    @Column(name = "REQENCRYPTIONID")
    public String getReqencryptionId() {
        return reqencryptionId;
    }

    public void setReqencryptionId(String reqencryptionId) {
        this.reqencryptionId = reqencryptionId;
    }

    @Basic
    @Column(name = "RESENCRYPTIONID")
    public String getResencryptionId() {
        return resencryptionId;
    }

    public void setResencryptionId(String resencryptionId) {
        this.resencryptionId = resencryptionId;
    }

    @Basic
    @Column(name = "MANDATORY")
    public String getMandatory() {
        return mandatory;
    }

    public void setMandatory(String mandatory) {
        this.mandatory = mandatory;
    }

    @Basic
    @Column(name = "FUSE")
    public String getFuse() {
        return fuse;
    }

    public void setFuse(String fuse) {
        this.fuse = fuse;
    }

    @Basic
    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Basic
    @Column(name = "GROUPNO")
    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    @Basic
    @Column(name = "ISTRUE")
    public String getIstrue() {
        return istrue;
    }

    public void setIstrue(String istrue) {
        this.istrue = istrue;
    }

    @Basic
    @Column(name = "TIMETHRESHOLD")
    public Long getTimethreshold() {
        return timethreshold;
    }

    public void setTimethreshold(Long timethreshold) {
        this.timethreshold = timethreshold;
    }

    @Basic
    @Column(name = "DESCS")
    public String getDescs() {
        return descs;
    }

    public void setDescs(String descs) {
        this.descs = descs;
    }


}
