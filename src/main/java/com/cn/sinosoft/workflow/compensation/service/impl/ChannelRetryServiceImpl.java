package com.cn.sinosoft.workflow.compensation.service.impl;

import com.cn.sinosoft.workflow.compensation.controller.ChannelController;
import com.cn.sinosoft.workflow.compensation.mq.RabbitProducer;
import com.cn.sinosoft.workflow.compensation.service.ChannelRetryService;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */
@Service
public class ChannelRetryServiceImpl implements ChannelRetryService {

    @Autowired
    RabbitProducer rabbitProducer;

    private static Logger log = LoggerFactory.getLogger(ChannelRetryServiceImpl.class);

    @Override
    public void compensationRetry(String data, Map<String ,String> map) {
        String retryTime = map.get("retryTime");

        if (retryTime != null) {
            if (Integer.parseInt(retryTime) > 3) {
                rabbitProducer.sendPolicyQueueDead(data);
                log.info("重试次数超过3次加入死信队列");
            }else{
                log.info("第{}次重试",retryTime);
                rabbitProducer.sendPolicyQueue(data,map);
            }
        }else {
            log.info("第1次重试");
            map.put("retryTime","1");
            rabbitProducer.sendPolicyQueue(data,map);
        }
    }
}
