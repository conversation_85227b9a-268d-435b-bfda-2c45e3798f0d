package com.cn.sinosoft.workflow.compensation.mq;


import com.cn.sinosoft.workflow.compensation.model.ConfigServerEntity;
import com.cn.sinosoft.workflow.compensation.model.ConfigWorkflowdetailedEntity;
import com.cn.sinosoft.workflow.compensation.repository.ConfigServerJPA;
import com.cn.sinosoft.workflow.compensation.repository.ConfigWorkflowdetailedJPA;
import com.cn.sinosoft.workflow.compensation.util.ConvertToJson;
import com.cn.sinosoft.workflow.compensation.util.Cxf;
import com.cn.sinosoft.workflow.compensation.util.HttpClient;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-10-14
 * describe
 */
@Component
@Slf4j

public class RabbitConsumer {

    @Autowired
    ConfigWorkflowdetailedJPA configWorkflowdetailedJPA;
    @Autowired
    ConfigServerJPA configServerJPA;
    @Autowired
    ConvertToJson convertToJson;

    // 高并发业务：在方法内做“批处理（3条一批）”，使用单消费者 + prefetch=3（见 RabbitMQConfig.highLoadContainerFactory）
    private static final int HIGH_LOAD_BATCH_SIZE = 3;
    private static final long HIGH_LOAD_FLUSH_MS = 115000L; // 超时未凑满3条则在2000ms后按已有条数处理
    // 批次缓冲：Key为Channel，Value为收集到的消息
    private final Map<Channel, List<HighLoadMsg>> highLoadBatchBuffer = new ConcurrentHashMap<>();
    // 每个Channel一个flush定时任务，用来在凑不满3条时超时触发处理
    private final Map<Channel, ScheduledFuture<?>> highLoadFlushTasks = new ConcurrentHashMap<>();
    // 每个Channel一个锁，避免监听线程与flush线程并发处理同一批次
    private final Map<Channel, Object> channelLocks = new ConcurrentHashMap<>();
    // 定时线程池
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "high-load-batch-flusher");
        t.setDaemon(true);
        return t;
    });

    private static class HighLoadMsg {
        final String body;
        final Message msg;
        final String serviceId;
        final long deliveryTag;
        HighLoadMsg(String body, Message msg, String serviceId) {
            this.body = body;
            this.msg = msg;
            this.serviceId = serviceId;
            this.deliveryTag = msg.getMessageProperties().getDeliveryTag();
        }
    }

    @RabbitListener(queues = MqConstant.RETRY_NORMAL_QUEUE2)
    public void reciveElePolicy(String queueMsg, Message msg, Channel channel,
                                @Header("retryTime") String retryTime,
                                @Header("workFlowId") String workflowid,
                                @Header("serviceId") String serviceId
                                ) throws IOException {

        log.info("收到消息：{}", queueMsg);
        try{
            ConfigWorkflowdetailedEntity byWorkflowidAndServiceId = configWorkflowdetailedJPA.findByWorkflowidAndServiceId(workflowid, serviceId);
            String serviceid = byWorkflowidAndServiceId.getServiceid();
            ConfigServerEntity cse1 = configServerJPA.findAllByServiceid(serviceid);
            String res = null;
            if (!StringUtils.isBlank(queueMsg)) {
                log.info("发送请求地址：{}",cse1.getUrlpath());
                switch (cse1.getCalltype()) {
                    case "hx-raw":
                        res = HttpClient.doPostString(cse1.getUrlpath(), queueMsg);
                        break;
                    case "wx-post":
                        res = HttpClient.readContentFromPost(queueMsg, cse1.getUrlpath());
                        break;
                    case "hx-post":
                        res = HttpClient.doPost(queueMsg, cse1.getUrlpath());
                        break;
                    case "cxf":
                        res = Cxf.cxfGo(cse1.getUrlpath(), cse1.getDescs(), queueMsg);
                        break;
                    case "fxq":
                        res = HttpClient.send(cse1.getUrlpath(), queueMsg);
                        break;
                    default:
                        res = HttpClient.sendPost(cse1.getUrlpath(), queueMsg);
                        break;
                }
                //如果配置了指定模板，则转换以后使用
                if (byWorkflowidAndServiceId.getRestemplateid()!=null) {
                    log.info("节点存在出单模板，执行模板转换");
                    res = convertToJson.requestJSON(res,byWorkflowidAndServiceId.getRestemplateid());
                }
                if (res.startsWith("{")) {
                    JSONObject Jsdata = new JSONObject(res);
                    if (Jsdata.has("data")) {
                        if (Jsdata.getJSONObject("data").has("common")) {
                            if (Jsdata.getJSONObject("data").getJSONObject("common").has("resCode")) {
                                String resCode = Jsdata.getJSONObject("data").getJSONObject("common").optString("resCode");
                                String resMsg = Jsdata.getJSONObject("data").getJSONObject("common").optString("resMsg");
                                if (!resCode.equals("01")) {
                                    if (resCode.equals("400")) {//服务调用异常
                                        this.produce(queueMsg,cse1.getServiceid(),workflowid,retryTime);
                                    }
                                    if (!resCode.equals("400")) {//业务异常
                                        this.produce(queueMsg,cse1.getServiceid(),workflowid,retryTime);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("消息处理失败：{}",e.getMessage());
            e.printStackTrace();
        }finally {
            //这段代码表示，这次消息，我已经接受并消费掉了，不会再重复发送消费
            channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
        }
    }
    public void produce(String req,String serviceId,String workFlowId,String retryTime){
        try {
            String mqServiceId = "zt-020-001";
            ConfigServerEntity cse1 = configServerJPA.findAllByServiceid(mqServiceId);//mq调用生产者地址
            Map<String, String> map = new HashMap<>();
            map.put("workFlowId", workFlowId);
            map.put("serviceId", serviceId);
            map.put("retryTime", String.valueOf((Integer.parseInt(retryTime)+1)));
            HttpClient.doPostWithHead(req, cse1.getUrlpath(), map);
            log.info("补偿机制发起完成");
        } catch (Exception e) {
            log.info("mq服务调用异常");
            e.printStackTrace();
        }
    }

    @RabbitListener(queues = MqConstant.HIGH_LOAD_NORMAL_QUEUE, containerFactory = "highLoadContainerFactory")
    public void highLoadConsumer(String queueMsg, Message msg, Channel channel, @Header("serviceId") String serviceId) throws IOException {
        Object lock = channelLocks.computeIfAbsent(channel, ch -> new Object());
        List<HighLoadMsg> snapshotToProcess = null;
        synchronized (lock) {
            List<HighLoadMsg> batch = highLoadBatchBuffer.computeIfAbsent(channel, ch -> new ArrayList<>());
            batch.add(new HighLoadMsg(queueMsg, msg, serviceId));
            log.info("高并发业务批次收集：当前批内数量={}", batch.size());
            if (batch.size() == 1) {
                // 首条进来就安排一个超时flush；如果在窗口内攒满3条，会在下方取消
                ScheduledFuture<?> prev = highLoadFlushTasks.get(channel);
                if (prev != null) {
                    prev.cancel(false);
                }
                ScheduledFuture<?> future = scheduler.schedule(() -> {
                    // 超时flush：在同一channel的锁下拿到快照处理
                    List<HighLoadMsg> toProcess = null;
                    synchronized (lock) {
                        List<HighLoadMsg> b = highLoadBatchBuffer.get(channel);
                        if (b != null && !b.isEmpty()) {
                            toProcess = new ArrayList<>(b);
                            b.clear();
                            highLoadFlushTasks.remove(channel);
                        } else {
                            highLoadFlushTasks.remove(channel);
                        }
                    }
                    if (toProcess != null && !toProcess.isEmpty()) {
                        log.info("高并发业务超时flush触发，数量={}", toProcess.size());
                        processHighLoadBatch(channel, toProcess);
                    }
                }, HIGH_LOAD_FLUSH_MS, TimeUnit.MILLISECONDS);
                highLoadFlushTasks.put(channel, future);
            }
            if (batch.size() >= HIGH_LOAD_BATCH_SIZE) {
                // 攒满3条，取消定时flush，立刻处理
                ScheduledFuture<?> prev = highLoadFlushTasks.remove(channel);
                if (prev != null) {
                    prev.cancel(false);
                }
                snapshotToProcess = new ArrayList<>(batch);
                batch.clear();
            }
        }
        if (snapshotToProcess != null && !snapshotToProcess.isEmpty()) {
            log.info("高并发业务开始处理批次：数量={}", snapshotToProcess.size());
            processHighLoadBatch(channel, snapshotToProcess);
        }
    }

    private void processHighLoadBatch(Channel channel, List<HighLoadMsg> batchItems) {
        for (int i = 0; i < batchItems.size(); i++) {
            HighLoadMsg item = batchItems.get(i);
            try {
                // 防御：serviceId 为空或查不到配置时，跳过HTTP调用，直接ACK，避免NPE，便于仅RabbitMQ验证
                if (StringUtils.isBlank(item.serviceId)) {
                    log.warn("[高并发] serviceId缺失，跳过HTTP调用，仅ACK此消息。body={}", item.body);
                    channel.basicAck(item.deliveryTag, false);
                    continue;
                }
                ConfigServerEntity configServer = configServerJPA.findAllByServiceid(item.serviceId);
                if (configServer == null) {
                    log.warn("[高并发] serviceId={} 未找到下游配置，跳过HTTP调用，仅ACK此消息。body={}", item.serviceId, item.body);
                    channel.basicAck(item.deliveryTag, false);
                    continue;
                }
                String res = null;
                if (!StringUtils.isBlank(item.body)) {
                    log.info("发送请求地址：{}", configServer.getUrlpath());
                    switch (configServer.getCalltype()) {
                        case "hx-raw":
                            res = HttpClient.doPostString(configServer.getUrlpath(), item.body);
                            break;
                        case "wx-post":
                            res = HttpClient.readContentFromPost(item.body, configServer.getUrlpath());
                            break;
                        case "hx-post":
                            res = HttpClient.doPost(item.body, configServer.getUrlpath());
                            break;
                        case "cxf":
                            res = Cxf.cxfGo(configServer.getUrlpath(), configServer.getDescs(), item.body);
                            break;
                        case "fxq":
                            res = HttpClient.send(configServer.getUrlpath(), item.body);
                            break;
                        default:
                            res = HttpClient.sendPost(configServer.getUrlpath(), item.body);
                            break;
                    }
                    log.info("高并发业务消费接口响应日志：{}", res);
                }
                // 每条单独确认，保证不丢消息
                channel.basicAck(item.deliveryTag, false);
            } catch (Exception ex) {
                log.error("批次中第{}条消息处理失败，将重回队列：{}", i + 1, ex.getMessage(), ex);
                try {
                    channel.basicNack(item.deliveryTag, false, true);
                } catch (IOException ioException) {
                    log.error("basicNack异常：{}", ioException.getMessage(), ioException);
                }
            }
        }
    }
//                String queueMsg = queueMsgs.get(i);
//                String serviceId = serviceIds.get(i);
//                Message msg = messages.get(i);
//
//                log.info("处理第{}条消息：{}", i+1, queueMsg);
//
//                ConfigServerEntity configServer = configServerJPA.findAllByServiceid(serviceId);
//                String res = null;
//
//                if (!StringUtils.isBlank(queueMsg)) {
//                    log.info("发送请求地址：{}", configServer.getUrlpath());
//
//                    switch (configServer.getCalltype()) {
//                        case "hx-raw":
//                            res = HttpClient.doPostString(configServer.getUrlpath(), queueMsg);
//                            break;
//                        case "wx-post":
//                            res = HttpClient.readContentFromPost(queueMsg, configServer.getUrlpath());
//                            break;
//                        case "hx-post":
//                            res = HttpClient.doPost(queueMsg, configServer.getUrlpath());
//                            break;
//                        case "cxf":
//                            res = Cxf.cxfGo(configServer.getUrlpath(), configServer.getDescs(), queueMsg);
//                            break;
//                        case "fxq":
//                            res = HttpClient.send(configServer.getUrlpath(), queueMsg);
//                            break;
//                        default:
//                            res = HttpClient.sendPost(configServer.getUrlpath(), queueMsg);
//                            break;
//                    }
//                    log.info("高并发业务消费接口响应日志：{}", res);
//                }
//            }
//        } catch (Exception e) {
//            log.error("批量消息处理失败：{}", e.getMessage());
//            e.printStackTrace();
//        } finally {
//            // 批量确认所有处理过的消息
//            for (Message msg : messages) {
//                channel.basicAck(msg.getMessageProperties().getDeliveryTag(), false);
//            }
//        }
//    }
}
