package com.cn.sinosoft.workflow.compensation.model;

import javax.persistence.*;
import java.util.Objects;

/**
 * 模板表
 */
@Entity
@Table(name = "CONFIG_TEMPLATE")
public class ConfigTemplateEntity {
    //模板ID 格式001001001
    private String templateid;
    //模板文件名称
    private String templatename;
    //版本号
    private String version;
    //报文格式
    private String format;
    //模板类型0：发送1：接收
    private String type;
    //模板路径
    private String templatepath;
    //备注
    private String descs;

    @Id
    @Column(name = "TEMPLATEID")
    public String getTemplateid() {
        return templateid;
    }

    public void setTemplateid(String templateid) {
        this.templateid = templateid;
    }

    @Basic
    @Column(name = "TEMPLATENAME")
    public String getTemplatename() {
        return templatename;
    }

    public void setTemplatename(String templatename) {
        this.templatename = templatename;
    }

    @Basic
    @Column(name = "VERSION")
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Basic
    @Column(name = "FORMAT")
    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    @Basic
    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Basic
    @Column(name = "TEMPLATEPATH")
    public String getTemplatepath() {
        return templatepath;
    }

    public void setTemplatepath(String templatepath) {
        this.templatepath = templatepath;
    }

    @Basic
    @Column(name = "DESCS")
    public String getDescs() {
        return descs;
    }

    public void setDescs(String descs) {
        this.descs = descs;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfigTemplateEntity that = (ConfigTemplateEntity) o;
        return Objects.equals(templateid, that.templateid) &&
                Objects.equals(version, that.version) &&
                Objects.equals(format, that.format) &&
                Objects.equals(type, that.type) &&
                Objects.equals(templatepath, that.templatepath) &&
                Objects.equals(descs, that.descs);
    }

    @Override
    public int hashCode() {

        return Objects.hash(templateid, version, format, type, templatepath, descs);
    }
}
