package com.cn.sinosoft.workflow.compensation;

import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
//import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.scheduling.annotation.EnableAsync;

//@EnableEurekaClient //引入eureka客户端
@EnableDiscoveryClient//引入ribbon负载均衡
@SpringBootApplication
@EnableAsync//开启异步
@EnableRabbit
public class CompensationApplication {

    public static void main(String[] args) {
        SpringApplication.run(CompensationApplication.class, args);
    }

}
